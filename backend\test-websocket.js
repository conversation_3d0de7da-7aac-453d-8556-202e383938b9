const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const mongoose = require('mongoose');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
  },
});

app.use(express.json());

// Connexion MongoDB
mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true })
  .then(() => console.log("✅ MongoDB connecté"))
  .catch(err => console.log("❌ Erreur MongoDB:", err));

// Test des modèles
const { Conversation, Message } = require('./models/Chat');
const User = require('./models/User');

// Socket.IO : Gestion des connexions
io.on('connection', (socket) => {
  console.log('🔌 Nouvelle connexion Socket.IO:', socket.id);

  // Rejoindre une room basée sur l'ID utilisateur
  socket.on('join', (userId) => {
    socket.join(userId);
    console.log(`👤 Utilisateur ${userId} a rejoint la room`);
  });

  // Écouter l'envoi de messages
  socket.on('sendMessage', async (data) => {
    const { senderId, receiverId, content, messageType = 'text' } = data;
    console.log('📨 Nouveau message:', { senderId, receiverId, content: content.substring(0, 50) + '...' });
    
    try {
      const receiver = await User.findById(receiverId);
      if (!receiver) {
        socket.emit('error', { message: 'Destinataire non trouvé' });
        return;
      }

      let conversation = await Conversation.findOne({
        participants: { $all: [senderId, receiverId] },
        isActive: true,
      });

      if (!conversation) {
        conversation = new Conversation({
          participants: [senderId, receiverId],
        });
        await conversation.save();
        console.log('💬 Nouvelle conversation créée:', conversation._id);
      }

      const message = new Message({
        conversationId: conversation._id,
        senderId,
        receiverId,
        content,
        messageType,
      });

      await message.save();
      await message.populate('senderId', 'nom prenom photoUrl');
      await message.populate('receiverId', 'nom prenom photoUrl');

      console.log('✅ Message sauvegardé, envoi aux utilisateurs...');

      // Émettre le message aux deux utilisateurs
      io.to(senderId).emit('newMessage', message);
      io.to(receiverId).emit('newMessage', message);

      console.log(`📤 Message envoyé à ${senderId} et ${receiverId}`);

      // Mettre à jour la conversation
      await Conversation.findByIdAndUpdate(conversation._id, {
        lastMessage: message._id,
        lastMessageTime: message.createdAt,
      });

    } catch (error) {
      console.error('❌ Erreur lors de l\'envoi du message via Socket.IO:', error);
      socket.emit('error', { message: 'Erreur serveur', error: error.message });
    }
  });

  socket.on('disconnect', () => {
    console.log('🔌 Déconnexion Socket.IO:', socket.id);
  });
});

// Route de test
app.get('/test', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Serveur WebSocket de test fonctionnel',
    timestamp: new Date().toISOString(),
    connections: io.engine.clientsCount
  });
});

const PORT = process.env.PORT || 5001;
server.listen(PORT, () => {
  console.log(`🚀 Serveur WebSocket de test démarré sur le port ${PORT}`);
  console.log(`📡 Test: http://localhost:${PORT}/test`);
  console.log('🔧 WebSocket disponible pour les tests');
});
