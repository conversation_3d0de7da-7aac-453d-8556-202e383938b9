const axios = require('axios');
require('dotenv').config();

// Configuration
const BASE_URL = process.env.BASE_URL || 'https://abe4bbb5eca9.ngrok-free.app';

// Headers communs
const headers = {
  'Content-Type': 'application/json',
  'ngrok-skip-browser-warning': 'true',
  'User-Agent': 'Test Script Client',
  'Accept': 'application/json'
};

console.log('🧪 Test du chat côté CLIENT');
console.log(`🌐 URL de base: ${BASE_URL}`);

async function testEndpoint(endpoint, method = 'GET', data = null, authToken = null) {
  try {
    console.log(`\n📋 Test ${method} ${endpoint}`);
    
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: { ...headers },
      timeout: 30000
    };
    
    if (authToken) {
      config.headers['Authorization'] = `Bearer ${authToken}`;
    }
    
    if (data && (method === 'POST' || method === 'PUT')) {
      config.data = data;
    }
    
    const response = await axios(config);
    console.log(`✅ Succès - Status: ${response.status}`);
    console.log(`📄 Réponse: ${JSON.stringify(response.data, null, 2)}`);
    return { success: true, status: response.status, data: response.data };
  } catch (error) {
    console.log(`❌ Erreur - Status: ${error.response?.status || 'N/A'}`);
    console.log(`📄 Message: ${error.response?.data?.message || error.message}`);
    console.log(`📄 Réponse complète: ${JSON.stringify(error.response?.data, null, 2)}`);
    return { success: false, status: error.response?.status, error: error.response?.data || error.message };
  }
}

async function loginAsClient() {
  console.log('\n🔐 Tentative de connexion en tant que client...');
  
  // Essayons avec des identifiants de client de test
  const clientCredentials = [
    { email: '<EMAIL>', password: 'test123' },
    { email: '<EMAIL>', password: 'test123' },
    { email: '<EMAIL>', password: 'password123' }
  ];
  
  for (const creds of clientCredentials) {
    console.log(`\n🔍 Essai avec ${creds.email}...`);
    
    const result = await testEndpoint('/api/auth/login', 'POST', creds);
    
    if (result.success && result.data.token) {
      console.log(`✅ Connexion réussie pour ${creds.email}`);
      console.log(`🔑 Token: ${result.data.token.substring(0, 50)}...`);
      console.log(`👤 Utilisateur: ${JSON.stringify(result.data.user, null, 2)}`);
      return result.data.token;
    }
  }
  
  console.log('❌ Aucune connexion client réussie');
  return null;
}

async function testChatWithClientToken(token) {
  console.log('\n🧪 Test des endpoints de chat avec token client...');
  
  // Test 1: Récupérer les conversations
  await testEndpoint('/api/chat/conversations', 'GET', null, token);
  
  // Test 2: Recherche d'utilisateurs (conducteurs)
  await testEndpoint('/api/chat/users/search?query=ghada', 'GET', null, token);
  
  // Test 3: Recherche avec rôle spécifique
  await testEndpoint('/api/chat/users/search?query=test&role=conducteur', 'GET', null, token);
  
  // Test 4: Compteur de messages non lus
  await testEndpoint('/api/chat/messages/unread-count', 'GET', null, token);
}

async function runTests() {
  console.log('\n🚀 Démarrage des tests côté client...\n');
  
  // Test 1: Health check
  await testEndpoint('/api/health');
  
  // Test 2: Connexion en tant que client
  const clientToken = await loginAsClient();
  
  if (clientToken) {
    // Test 3: Tests de chat avec le token client
    await testChatWithClientToken(clientToken);
  } else {
    console.log('\n❌ Impossible de tester le chat sans token client valide');
    console.log('💡 Créez un utilisateur client de test ou vérifiez les identifiants');
  }
  
  console.log('\n✅ Tests terminés');
}

// Exécuter les tests
runTests().catch(console.error);
