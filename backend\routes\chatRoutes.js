const express = require('express');
const router = express.Router();
const authMiddleware = require('../middlewares/authMiddleware');
const {
  getUserConversations,
  getConversationMessages,
  sendMessage,
  searchUsers,
  markConversationAsRead,
  getUnreadCount
} = require('../controllers/chatController');

router.use(authMiddleware);

router.get('/conversations', getUserConversations);
router.get('/conversations/:conversationId/messages', getConversationMessages);
router.put('/conversations/:conversationId/read', markConversationAsRead);
router.post('/messages', sendMessage);
router.get('/messages/unread-count', getUnreadCount);
router.get('/users/search', searchUsers);

module.exports = router;