class ApiConfig {
  // 🌍 URL de base de l'API - Mise à jour automatique nécessaire
  // IMPORTANT: Cette URL doit être mise à jour à chaque redémarrage de ngrok

  // 🔧 SOLUTION TEMPORAIRE: Utilisez localhost pour les tests
  // Décommentez la ligne suivante et commentez l'URL ngrok si ngrok ne fonctionne pas
  // static const String baseUrl = 'http://localhost:5000';

  // URL ngrok (à mettre à jour avec fix-ngrok-url-now.ps1)
  static const String baseUrl = 'https://776a96d5b112.ngrok-free.app';

  // 🛣️ Endpoints selon ton backend
  static const String loginEndpoint = '/api/auth/login';
  static const String registerEndpoint = '/api/users/register';
  static const String passwordEndpoint = '/api/auth/password';

  // 📩 Headers communs
  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'ngrok-skip-browser-warning': 'true',
    'User-Agent': 'Flutter App',
    'Cache-Control': 'no-cache',
  };

  // 🔗 URLs complètes
  static String get loginUrl => '$baseUrl$loginEndpoint';
  static String get registerUrl => '$baseUrl$registerEndpoint';
  static String get passwordUrl => '$baseUrl$passwordEndpoint';

  // ⚠️ Messages d'erreur standardisés (HTTP)
  static String getErrorMessage(int statusCode, {String? defaultMessage}) {
    switch (statusCode) {
      case 400:
        return 'Données invalides. Vérifiez vos informations.';
      case 401:
        return 'Email ou mot de passe incorrect.';
      case 403:
        return 'Accès refusé. Vous n\'avez pas les droits nécessaires.';
      case 404:
        return 'Service non disponible. Vérifiez que le serveur est démarré.';
      case 409:
        return 'Un compte avec cet email ou CIN existe déjà.';
      case 422:
        return 'Données invalides ou manquantes.';
      case 500:
      case 502:
      case 503:
        return 'Erreur serveur. Veuillez réessayer plus tard.';
      default:
        return defaultMessage ?? 'Erreur inconnue (Code: $statusCode).';
    }
  }

  // 📶 Messages d'erreur de connexion
  static String getConnectionErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();
    if (errorString.contains('socketexception') ||
        errorString.contains('handshakeexception')) {
      return 'Impossible de se connecter au serveur. Vérifiez votre connexion internet et que le serveur est démarré.';
    } else if (errorString.contains('timeoutexception')) {
      return 'Délai d\'attente dépassé. Vérifiez votre connexion internet.';
    } else if (errorString.contains('formatexception')) {
      return 'Erreur de format de réponse du serveur.';
    } else if (errorString.contains('clientexception')) {
      return 'Impossible de se connecter au serveur. Vérifiez que le serveur est démarré et accessible via ngrok.';
    } else if (errorString.contains('failed to fetch')) {
      return 'Échec de la requête réseau. Vérifiez que le serveur backend est démarré et que l\'URL ngrok est correcte.';
    } else {
      return 'Erreur de connexion au serveur. Détails: ${error.toString()}';
    }
  }
}