import 'dart:async';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/api_config.dart';
import 'chat_service.dart';

class WebSocketService {
  static IO.Socket? _socket;
  static bool _isConnected = false;
  static String? _currentUserId;
  
  // Stream controllers pour les événements
  static final StreamController<Message> _messageController = StreamController<Message>.broadcast();
  static final StreamController<bool> _connectionController = StreamController<bool>.broadcast();
  
  // Getters pour les streams
  static Stream<Message> get messageStream => _messageController.stream;
  static Stream<bool> get connectionStream => _connectionController.stream;
  static bool get isConnected => _isConnected;

  // Initialiser la connexion WebSocket
  static Future<void> connect() async {
    try {
      // Récupérer le token et l'ID utilisateur
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');
      final userId = prefs.getString('userId');
      
      if (token == null || userId == null) {
        print('❌ Token ou userId manquant pour WebSocket');
        return;
      }

      _currentUserId = userId;
      
      // Configuration Socket.IO
      _socket = IO.io(ApiConfig.baseUrl, <String, dynamic>{
        'transports': ['websocket'],
        'autoConnect': false,
        'auth': {
          'token': token,
        },
        'extraHeaders': {
          'Authorization': 'Bearer $token',
        }
      });

      // Événements de connexion
      _socket!.onConnect((_) {
        print('✅ WebSocket connecté');
        _isConnected = true;
        _connectionController.add(true);
        
        // Rejoindre la room de l'utilisateur
        _socket!.emit('join', userId);
      });

      _socket!.onDisconnect((_) {
        print('❌ WebSocket déconnecté');
        _isConnected = false;
        _connectionController.add(false);
      });

      _socket!.onConnectError((error) {
        print('❌ Erreur de connexion WebSocket: $error');
        _isConnected = false;
        _connectionController.add(false);
      });

      // Écouter les nouveaux messages
      _socket!.on('newMessage', (data) {
        try {
          print('📨 Nouveau message reçu: $data');
          final message = Message.fromJson(data);
          _messageController.add(message);
        } catch (e) {
          print('❌ Erreur lors du parsing du message: $e');
        }
      });

      // Écouter les erreurs
      _socket!.on('error', (error) {
        print('❌ Erreur WebSocket: $error');
      });

      // Se connecter
      _socket!.connect();
      
    } catch (e) {
      print('❌ Erreur lors de l\'initialisation WebSocket: $e');
    }
  }

  // Envoyer un message via WebSocket
  static void sendMessage(String receiverId, String content, {String messageType = 'text'}) {
    if (_socket != null && _isConnected && _currentUserId != null) {
      _socket!.emit('sendMessage', {
        'senderId': _currentUserId,
        'receiverId': receiverId,
        'content': content,
        'messageType': messageType,
      });
    } else {
      print('❌ WebSocket non connecté, impossible d\'envoyer le message');
    }
  }

  // Rejoindre une conversation
  static void joinConversation(String conversationId) {
    if (_socket != null && _isConnected) {
      _socket!.emit('joinConversation', conversationId);
    }
  }

  // Quitter une conversation
  static void leaveConversation(String conversationId) {
    if (_socket != null && _isConnected) {
      _socket!.emit('leaveConversation', conversationId);
    }
  }

  // Marquer un message comme lu
  static void markMessageAsRead(String messageId) {
    if (_socket != null && _isConnected) {
      _socket!.emit('markAsRead', messageId);
    }
  }

  // Déconnecter WebSocket
  static void disconnect() {
    if (_socket != null) {
      _socket!.disconnect();
      _socket!.dispose();
      _socket = null;
      _isConnected = false;
      _connectionController.add(false);
    }
  }

  // Nettoyer les ressources
  static void dispose() {
    disconnect();
    _messageController.close();
    _connectionController.close();
  }

  // Reconnecter si nécessaire
  static Future<void> reconnectIfNeeded() async {
    if (!_isConnected && _socket == null) {
      await connect();
    }
  }
}
