import 'package:flutter/material.dart';
import '../../services/api_service.dart';
import '../../services/auth_service.dart';
import '../../services/websocket_service.dart';
import '../../config/api_config.dart';
import 'dart:convert';

class AccountContent extends StatefulWidget {
  final String userId;

  const AccountContent({Key? key, required this.userId}) : super(key: key);

  @override
  _AccountContentState createState() => _AccountContentState();
}

class _AccountContentState extends State<AccountContent> {
  Map<String, dynamic>? userData;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchUserData();
  }

  Future<void> _fetchUserData() async {
    try {
      final token = await AuthService.getToken();
      print('Token utilisé : $token'); // Log du token
      final response = await ApiService.get(
        '${ApiConfig.baseUrl}/api/users/${widget.userId}',
        token: token,
      );

      if (response.isSuccess) {
        if (mounted) {
          final data = jsonDecode(response.body);
          print('Données utilisateur reçues : $data'); // Log des données
          setState(() {
            userData = data;
            isLoading = false;
            // Vérifier si cin ou age sont manquants
            if (userData!['cin'] == null || userData!['age'] == null) {
              errorMessage = 'Certains champs (CIN ou Âge) ne sont pas fournis par le serveur.';
              print('Erreur : CIN ou Âge manquant dans les données reçues');
            }
          });
        }
      } else {
        if (mounted) {
          final errorData = jsonDecode(response.body);
          print('Erreur API : $errorData'); // Log de l'erreur
          setState(() {
            errorMessage = errorData['message'] ?? 'Erreur lors de la récupération des données';
            isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        print('Exception lors de la récupération des données : $e'); // Log de l'exception
        setState(() {
          errorMessage = e.toString().replaceFirst('Exception: ', '');
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "👤 Profil Utilisateur",
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 16),
          if (isLoading)
            Center(child: CircularProgressIndicator())
          else if (errorMessage != null)
            Center(
              child: Column(
                children: [
                  Text(
                    "Erreur: $errorMessage",
                    style: TextStyle(color: Colors.red, fontSize: 16),
                  ),
                  SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _fetchUserData,
                    child: Text("Réessayer"),
                  ),
                ],
              ),
            )
          else
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Card(
                      elevation: 2,
                      child: ListTile(
                        leading: userData!['photoUrl'] != null
                            ? CircleAvatar(
                                backgroundImage: NetworkImage(userData!['photoUrl']),
                              )
                            : CircleAvatar(child: Icon(Icons.person)),
                        title: Text(
                          "${userData!['nom'] ?? 'Non défini'} ${userData!['prenom'] ?? 'Non défini'}",
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        subtitle: Text(userData!['role'] ?? 'client'),
                      ),
                    ),
                    SizedBox(height: 16),
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildInfoRow("Nom", userData!['nom'] ?? 'Non défini'),
                            _buildInfoRow("Prénom", userData!['prenom'] ?? 'Non défini'),
                            _buildInfoRow("CIN", userData!['cin'] ?? 'Non défini'),
                            _buildInfoRow("Âge", userData!['age']?.toString() ?? 'Non défini'),
                            _buildInfoRow("Email", userData!['email'] ?? 'Non défini'),
                            _buildInfoRow("Téléphone", userData!['phone'] ?? 'Non défini'),
                            _buildInfoRow("Ville", userData!['ville'] ?? 'Non défini'),
                            if (userData!['role'] == 'conducteur')
                              _buildInfoRow("Numéro de permis", userData!['numero_permis'] ?? 'Non défini'),
                            _buildInfoRow(
                              "Compte vérifié",
                              userData!['isVerified'] == true ? 'Oui' : 'Non',
                            ),
                            _buildInfoRow(
                              "Date de création",
                              userData!['createdAt'] != null
                                  ? DateTime.parse(userData!['createdAt']).toLocal().toString()
                                  : 'Non défini',
                            ),
                            _buildInfoRow(
                              "Dernière mise à jour",
                              userData!['updatedAt'] != null
                                  ? DateTime.parse(userData!['updatedAt']).toLocal().toString()
                                  : 'Non défini',
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 16),
                    Center(
                      child: ElevatedButton(
                        onPressed: () async {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text("Déconnexion")),
                          );

                          // Déconnecter WebSocket
                          WebSocketService.disconnect();

                          // Effacer le token
                          await AuthService.clearToken();

                          if (!mounted) return;

                          Navigator.pushNamedAndRemoveUntil(context, '/login', (route) => false);
                        },
                        child: Text("Se déconnecter"),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(
              label,
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }
}