{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.12.2", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "image-size": "^2.0.2", "image-type": "^6.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.18.1", "multer": "^2.0.2", "socket.io": "^4.8.1"}, "description": ""}