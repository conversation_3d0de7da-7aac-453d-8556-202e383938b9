const express = require('express');
const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
require('dotenv').config();

const app = express();
app.use(express.json());

// Connexion MongoDB
mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true })
  .then(() => console.log("✅ MongoDB connecté"))
  .catch(err => console.log("❌ Erreur MongoDB:", err));

const User = require('./models/User');

// Test de login pour vérifier les données retournées
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    console.log('🔍 Tentative de connexion:', { email });

    const user = await User.findOne({ email });
    if (!user) {
      console.log('❌ Utilisateur non trouvé');
      return res.status(401).json({ message: 'Adresse e-mail ou mot de passe incorrect.' });
    }

    console.log('✅ Utilisateur trouvé:', {
      id: user._id,
      nom: user.nom,
      prenom: user.prenom,
      role: user.role,
      email: user.email
    });

    // Générer un token JWT
    const token = jwt.sign(
      { userId: user._id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );

    const response = {
      message: 'Connexion réussie',
      token,
      user: {
        id: user._id.toString(),
        role: user.role,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email
      }
    };

    console.log('📤 Réponse envoyée:', response);
    res.json(response);

  } catch (error) {
    console.error('❌ Erreur lors de la connexion:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
});

// Test des conversations
const authMiddleware = (req, res, next) => {
  const token = req.header('Authorization')?.replace('Bearer ', '');
  if (!token) {
    return res.status(401).json({ message: 'Authentification requise' });
  }
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = { id: decoded.userId, role: decoded.role };
    next();
  } catch (error) {
    res.status(401).json({ message: 'Token invalide' });
  }
};

app.get('/api/chat/conversations', authMiddleware, async (req, res) => {
  try {
    const userId = req.user.id;
    console.log('🔍 Récupération conversations pour:', userId);

    const { Conversation } = require('./models/Chat');
    
    const conversations = await Conversation.find({
      participants: userId,
      isActive: true
    })
    .populate('participants', 'nom prenom photoUrl role ville')
    .populate({
      path: 'lastMessage',
      select: 'content createdAt senderId receiverId messageType',
      populate: [
        { path: 'senderId', select: 'nom prenom photoUrl role' },
        { path: 'receiverId', select: 'nom prenom photoUrl role' }
      ]
    })
    .sort({ lastMessageTime: -1 });

    console.log('✅ Conversations trouvées:', conversations.length);
    console.log('📤 Première conversation:', JSON.stringify(conversations[0], null, 2));

    res.json(conversations);
  } catch (error) {
    console.error('❌ Erreur conversations:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
});

const PORT = 5002;
app.listen(PORT, () => {
  console.log(`🚀 Serveur de test login/WebSocket sur le port ${PORT}`);
  console.log(`📡 Test login: POST http://localhost:${PORT}/api/auth/login`);
  console.log(`📡 Test conversations: GET http://localhost:${PORT}/api/chat/conversations`);
});
