const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Importer le modèle User
const User = require('./models/User');

async function createTestUsers() {
  try {
    // Connexion à MongoDB
    await mongoose.connect(process.env.MONGO_URI, { 
      useNewUrlParser: true, 
      useUnifiedTopology: true 
    });
    console.log('✅ Connecté à MongoDB');

    // Créer un client de test
    const clientData = {
      nom: 'TestClient',
      prenom: 'Client',
      email: '<EMAIL>',
      role: 'client',
      cin: 'CL123456',
      telephone: '0612345678',
      ville: 'Casablanca',
      isVerified: true,
      passwordHash: await bcrypt.hash('test123', 10)
    };

    // Vérifier si le client existe déjà
    const existingClient = await User.findOne({ email: clientData.email });
    if (existingClient) {
      console.log('👤 Client de test existe déjà:', existingClient.email);
    } else {
      const client = new User(clientData);
      await client.save();
      console.log('✅ Client de test créé:', client.email);
    }

    // Créer un conducteur de test
    const conducteurData = {
      nom: 'TestConducteur',
      prenom: 'Conducteur',
      email: '<EMAIL>',
      role: 'conducteur',
      cin: 'CD123456',
      telephone: '0612345679',
      ville: 'Rabat',
      isVerified: true,
      passwordHash: await bcrypt.hash('test123', 10)
    };

    // Vérifier si le conducteur existe déjà
    const existingConducteur = await User.findOne({ email: conducteurData.email });
    if (existingConducteur) {
      console.log('👤 Conducteur de test existe déjà:', existingConducteur.email);
    } else {
      const conducteur = new User(conducteurData);
      await conducteur.save();
      console.log('✅ Conducteur de test créé:', conducteur.email);
    }

    // Lister tous les utilisateurs pour vérification
    console.log('\n📋 Liste des utilisateurs:');
    const users = await User.find({}, 'nom prenom email role').limit(10);
    users.forEach(user => {
      console.log(`- ${user.prenom} ${user.nom} (${user.email}) - ${user.role}`);
    });

  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Déconnecté de MongoDB');
  }
}

// Exécuter le script
createTestUsers();
