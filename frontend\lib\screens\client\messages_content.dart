import 'package:flutter/material.dart';
import 'dart:async';
import '../../services/chat_service.dart';
import '../../services/websocket_service.dart';
import '../../widgets/connection_status_widget.dart';
import 'conversation_screen.dart';
import 'new_message_screen.dart';

class MessagesContent extends StatefulWidget {
  const MessagesContent({Key? key}) : super(key: key);

  @override
  _MessagesContentState createState() => _MessagesContentState();
}

class _MessagesContentState extends State<MessagesContent> {
  List<Conversation> conversations = [];
  bool isLoading = true;
  String? error;
  StreamSubscription<Message>? _messageSubscription;

  @override
  void initState() {
    super.initState();
    _loadConversations();
    _initializeWebSocket();
  }

  @override
  void dispose() {
    _messageSubscription?.cancel();
    super.dispose();
  }

  void _initializeWebSocket() {
    // S'assurer que WebSocket est connecté
    WebSocketService.reconnectIfNeeded();

    // Écouter les nouveaux messages
    _messageSubscription = WebSocketService.messageStream.listen((message) {
      // Mettre à jour la liste des conversations quand un nouveau message arrive
      _updateConversationWithNewMessage(message);
    });
  }

  void _updateConversationWithNewMessage(Message message) {
    setState(() {
      // Trouver la conversation correspondante
      final conversationIndex = conversations.indexWhere(
        (conv) => conv.id == message.conversationId
      );

      if (conversationIndex != -1) {
        // Mettre à jour la conversation existante
        final updatedConversation = Conversation(
          id: conversations[conversationIndex].id,
          participants: conversations[conversationIndex].participants,
          lastMessage: message,
          lastMessageTime: message.createdAt,
          isActive: conversations[conversationIndex].isActive,
        );

        conversations[conversationIndex] = updatedConversation;

        // Réorganiser la liste par date du dernier message
        conversations.sort((a, b) => b.lastMessageTime.compareTo(a.lastMessageTime));
      } else {
        // Nouvelle conversation - recharger la liste
        _loadConversations();
      }
    });
  }

  Future<void> _loadConversations() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });

      final loadedConversations = await ChatService.getUserConversations();

      setState(() {
        conversations = loadedConversations;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "💬 Messages",
                      style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 4),
                    ConnectionStatusWidget(),
                  ],
                ),
                IconButton(
                  icon: Icon(Icons.add_comment, color: Colors.blue),
                  onPressed: () => _startNewConversation(),
                ),
              ],
            ),
            SizedBox(height: 16),
            Expanded(
              child: _buildConversationsList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConversationsList() {
    if (isLoading) {
      return Center(child: CircularProgressIndicator());
    }

    if (error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Erreur de chargement',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              error!,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadConversations,
              child: Text('Réessayer'),
            ),
          ],
        ),
      );
    }

    if (conversations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Aucune conversation',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Commencez une nouvelle conversation avec un conducteur',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
            SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _startNewConversation,
              icon: Icon(Icons.add),
              label: Text('Nouvelle conversation'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadConversations,
      child: ListView.builder(
        itemCount: conversations.length,
        itemBuilder: (context, index) {
          final conversation = conversations[index];
          final otherUser = conversation.participants.isNotEmpty
              ? conversation.participants.first
              : null;

          if (otherUser == null) return SizedBox.shrink();

          return Card(
            margin: EdgeInsets.symmetric(vertical: 4),
            child: ListTile(
              leading: CircleAvatar(
                backgroundImage: otherUser.photoUrl != null
                    ? NetworkImage(otherUser.photoUrl!)
                    : null,
                child: otherUser.photoUrl == null
                    ? Text(otherUser.nom.isNotEmpty ? otherUser.nom[0].toUpperCase() : '?')
                    : null,
              ),
              title: Text(
                otherUser.fullName,
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: Text(
                conversation.lastMessage?.content ?? 'Aucun message',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    _formatTime(conversation.lastMessageTime),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  if (otherUser.role != null)
                    Container(
                      margin: EdgeInsets.only(top: 4),
                      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: otherUser.role == 'conducteur' ? Colors.blue : Colors.green,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        otherUser.role == 'conducteur' ? 'Conducteur' : 'Client',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                      ),
                    ),
                ],
              ),
              onTap: () => _openConversation(conversation, otherUser),
            ),
          );
        },
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}j';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}min';
    } else {
      return 'maintenant';
    }
  }

  void _openConversation(Conversation conversation, UserInfo otherUser) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ConversationScreen(
          conversation: conversation,
          otherUser: otherUser,
        ),
      ),
    ).then((_) => _loadConversations()); // Recharger après retour
  }

  void _startNewConversation() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NewMessageScreen(),
      ),
    ).then((_) => _loadConversations()); // Recharger après retour
  }
}