const http = require('http');
const https = require('https');

// Configuration
const BASE_URL = 'https://abe4bbb5eca9.ngrok-free.app';
const LOCAL_URL = 'http://localhost:5000';

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const options = {
      method: 'GET',
      headers: {
        'ngrok-skip-browser-warning': 'true',
        'User-Agent': 'Server-Check-Script',
        'Accept': 'application/json'
      },
      timeout: 10000
    };

    const req = client.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function checkEndpoint(url, description) {
  console.log(`\n📋 Test: ${description}`);
  console.log(`🌐 URL: ${url}`);
  
  try {
    const response = await makeRequest(url);
    console.log(`✅ Status: ${response.statusCode}`);
    
    if (response.statusCode === 200) {
      try {
        const jsonData = JSON.parse(response.body);
        console.log(`📄 Réponse: ${JSON.stringify(jsonData, null, 2)}`);
      } catch (e) {
        console.log(`📄 Réponse (texte): ${response.body.substring(0, 200)}...`);
      }
    } else {
      console.log(`📄 Réponse: ${response.body}`);
    }
    
    return response.statusCode === 200;
  } catch (error) {
    console.log(`❌ Erreur: ${error.message}`);
    return false;
  }
}

async function runChecks() {
  console.log('🔍 Vérification du statut du serveur backend...\n');
  
  // Test 1: Health check ngrok
  const ngrokHealthy = await checkEndpoint(`${BASE_URL}/api/health`, 'Health check (ngrok)');
  
  // Test 2: Health check local
  const localHealthy = await checkEndpoint(`${LOCAL_URL}/api/health`, 'Health check (local)');
  
  // Test 3: Routes de chat (ngrok)
  if (ngrokHealthy) {
    await checkEndpoint(`${BASE_URL}/api/chat/conversations`, 'Chat conversations (ngrok) - sans auth');
  }
  
  // Test 4: Routes de chat (local)
  if (localHealthy) {
    await checkEndpoint(`${LOCAL_URL}/api/chat/conversations`, 'Chat conversations (local) - sans auth');
  }
  
  // Résumé
  console.log('\n📊 RÉSUMÉ:');
  console.log(`🌐 Serveur ngrok (${BASE_URL}): ${ngrokHealthy ? '✅ Accessible' : '❌ Inaccessible'}`);
  console.log(`🏠 Serveur local (${LOCAL_URL}): ${localHealthy ? '✅ Accessible' : '❌ Inaccessible'}`);
  
  if (!ngrokHealthy && !localHealthy) {
    console.log('\n⚠️  PROBLÈME DÉTECTÉ:');
    console.log('- Aucun serveur backend n\'est accessible');
    console.log('- Vérifiez que le serveur Node.js est démarré');
    console.log('- Vérifiez que ngrok est configuré correctement');
    console.log('\n💡 SOLUTIONS:');
    console.log('1. Démarrer le serveur: cd backend && npm start');
    console.log('2. Vérifier ngrok: ngrok http 5000');
    console.log('3. Mettre à jour l\'URL dans ApiConfig.dart');
  } else if (ngrokHealthy) {
    console.log('\n✅ Le serveur ngrok fonctionne correctement');
  } else if (localHealthy) {
    console.log('\n✅ Le serveur local fonctionne correctement');
    console.log('💡 Conseil: Utilisez localhost dans ApiConfig.dart pour les tests');
  }
}

// Exécuter les vérifications
runChecks().catch(console.error);
