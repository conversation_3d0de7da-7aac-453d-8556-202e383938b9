# Debug WebSocket - Guide de Résolution

## 🐛 Problèmes Identifiés et Corrigés

### ❌ **Problème 1: userId manquant pour WebSocket**
**Erreur:** `❌ Token ou userId manquant pour WebSocket`

**Cause:** L'userId n'était pas sauvegardé dans SharedPreferences lors du login

**✅ Solution appliquée:**
1. Ajout de méthodes dans `AuthService` pour sauvegarder l'userId
2. Modification du login pour sauvegarder token + userId ensemble
3. WebSocket peut maintenant récupérer l'userId

### ❌ **Problème 2: Parsing des conversations échoue**
**Erreur:** `TypeError: null: type 'Null' is not a subtype of type 'String'`

**Cause:** Le `lastMessage` dans les conversations n'avait pas les données utilisateur complètes

**✅ Solution appliquée:**
1. Correction du backend pour populer `senderId` et `receiverId` dans `lastMessage`
2. Ajout des champs `role` dans la population
3. Le parsing Flutter peut maintenant créer les objets `Message` correctement

## 🔧 Modifications Apportées

### Backend (`controllers/chatController.js`)
```javascript
.populate({
  path: 'lastMessage',
  select: 'content createdAt senderId receiverId messageType',
  populate: [
    { path: 'senderId', select: 'nom prenom photoUrl role' },
    { path: 'receiverId', select: 'nom prenom photoUrl role' }
  ]
})
```

### Frontend (`services/auth_service.dart`)
```dart
static Future<void> saveUserData(String token, String userId) async {
  final prefs = await SharedPreferences.getInstance();
  await prefs.setString(_tokenKey, token);
  await prefs.setString(_userIdKey, userId);
}

static Future<String?> getUserId() async {
  final prefs = await SharedPreferences.getInstance();
  return prefs.getString(_userIdKey);
}
```

### Frontend (`screens/login_page.dart`)
```dart
// Stocker le token et l'userId
if (result.token != null && result.userId != null) {
  await AuthService.saveUserData(result.token!, result.userId!);
}
```

## 🧪 Tests à Effectuer

### Test 1: Vérifier la sauvegarde des données de connexion
1. Se connecter avec un compte
2. Vérifier dans les logs: `✅ Connexion réussie pour l'utilisateur: [ID]`
3. Ne plus voir: `❌ Token ou userId manquant pour WebSocket`

### Test 2: Vérifier le chargement des conversations
1. Aller dans Messages
2. Vérifier que les conversations se chargent sans erreur
3. Ne plus voir: `TypeError: null: type 'Null' is not a subtype of type 'String'`

### Test 3: Vérifier WebSocket
1. Indicateur "En ligne" doit être vert
2. Envoyer un message depuis un autre compte
3. Le message doit apparaître instantanément

## 🚀 Commandes de Test

### Démarrer le serveur principal
```bash
cd backend
node server.js
```

### Démarrer le serveur de test (optionnel)
```bash
cd backend
node test-login-websocket.js
```

### Lancer l'application Flutter
```bash
cd frontend
flutter pub get
flutter run
```

## 📊 Logs à Surveiller

### ✅ Logs de Succès
```
✅ Connexion réussie pour l'utilisateur: [ID]
✅ WebSocket connecté
👤 Utilisateur [ID] a rejoint la room
🔌 Nouvelle connexion Socket.IO: [socket-id]
```

### ❌ Logs d'Erreur à Éviter
```
❌ Token ou userId manquant pour WebSocket
TypeError: null: type 'Null' is not a subtype of type 'String'
❌ Erreur de connexion WebSocket
```

## 🎯 Résultat Attendu

Après ces corrections:
1. ✅ WebSocket se connecte automatiquement au login
2. ✅ Les conversations se chargent correctement
3. ✅ Les messages s'affichent en temps réel
4. ✅ L'indicateur de connexion est vert
5. ✅ Pas d'erreurs dans les logs

## 🔄 Si Problèmes Persistent

1. **Nettoyer les données de l'app:**
   ```bash
   flutter clean
   flutter pub get
   ```

2. **Vérifier les SharedPreferences:**
   - Se déconnecter/reconnecter
   - Vérifier que token et userId sont sauvegardés

3. **Redémarrer le serveur:**
   ```bash
   # Arrêter le serveur (Ctrl+C)
   node server.js
   ```

4. **Vérifier la base de données:**
   - Les utilisateurs existent
   - Les conversations ont des participants valides
