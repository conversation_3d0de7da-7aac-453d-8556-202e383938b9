{"accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "fr-FR"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 139}, "autofill": {"last_version_deduped": 139}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"window_placement": {"bottom": 817, "left": 837, "maximized": false, "right": 1589, "top": 2, "work_area_bottom": 860, "work_area_left": 0, "work_area_right": 1600, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 18002, "default_search_provider": {"choice_screen_random_shuffle_seed": "1307700512644324086", "guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "1ab2c2f1-e7d6-49ba-ada3-636ebdc0a381", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "139.0.7258.139"}, "gaia_cookie": {"changed_time": **********.631602, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_binary_data": "", "periodic_report_time_2": "*****************"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "65fe7926-32e5-4add-9951-d751331d7d08"}}, "intl": {"selected_languages": "fr-FR,fr,en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "C7I3vNxRMQ9QZSB0LTRlsDP3pvc+tgB3bZU9vcHFvoIIiFbXi1/LVYpeaAHVy+7nlYp9zE/ig28FWW7LbbkOjw=="}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 2}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_KLARNA": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "DIGITAL_CREDENTIALS_LOW_FRICTION": true, "EWALLET_MERCHANT_ALLOWLIST": true, "GLIC_ACTION_PAGE_BLOCK": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLAT_RATE_BENEFITS_BLOCKLIST": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "DIGITAL_CREDENTIALS_LOW_FRICTION": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "SAVED_TAB_GROUP": true, "V8_COMPILE_HINTS": true}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:49177,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:49487,*": {"expiration": "13410905210044822", "last_modified": "13403129210044836", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:50137,*": {"expiration": "13411000896387103", "last_modified": "13403224896387118", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:50204,*": {"expiration": "13410905580523987", "last_modified": "13403129580524000", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:50255,*": {"expiration": "13410804710589519", "last_modified": "13403028710589534", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:50681,*": {"expiration": "13410805242059165", "last_modified": "13403029242059179", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:50703,*": {"expiration": "13410906602547516", "last_modified": "13403130602547530", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:51176,*": {"expiration": "13411001547048840", "last_modified": "13403225547048855", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:51320,*": {"expiration": "13410906903876944", "last_modified": "13403130903876959", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:51430,*": {"expiration": "13410805717350543", "last_modified": "13403029717350556", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:52145,*": {"expiration": "13410810794068208", "last_modified": "13403034794068222", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:53305,*": {"expiration": "13410803272012484", "last_modified": "13403027272012497", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:53686,*": {"expiration": "13410803575991289", "last_modified": "13403027575991303", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:53973,*": {"expiration": "13410803940534072", "last_modified": "13403027940534087", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:54285,*": {"expiration": "13410910148980479", "last_modified": "13403134148980491", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:54433,*": {"expiration": "13410910651939790", "last_modified": "13403134651939804", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:54834,*": {"expiration": "13410911065153915", "last_modified": "13403135065153929", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:55221,*": {"expiration": "13410911436127152", "last_modified": "13403135436127166", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:55864,*": {"expiration": "13410912224546902", "last_modified": "13403136224546920", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:56407,*": {"expiration": "13410913904388941", "last_modified": "13403137904388956", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:57964,*": {"expiration": "13410993918042709", "last_modified": "13403217918042723", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:59204,*": {"expiration": "13410994877598843", "last_modified": "13403218877598858", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:60335,*": {"expiration": "13410646997360464", "last_modified": "13402870997360478", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:61972,*": {"expiration": "13410995420676269", "last_modified": "13403219420676284", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:62438,*": {"expiration": "13410650250079012", "last_modified": "13402874250079026", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:62459,*": {"expiration": "13410996260619429", "last_modified": "13403220260619442", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:63071,*": {"expiration": "13410480106452704", "last_modified": "13402704106452717", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:63241,*": {"expiration": "13410996701740186", "last_modified": "13403220701740200", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:63331,*": {"expiration": "13410480220203979", "last_modified": "13402704220203990", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:63665,*": {"expiration": "13410996884693759", "last_modified": "13403220884693772", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:63691,*": {"expiration": "13410650435571694", "last_modified": "13402874435571720", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:63899,*": {"expiration": "13410997405374949", "last_modified": "13403221405374961", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:63960,*": {"expiration": "13410650841109757", "last_modified": "13402874841109771", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:64238,*": {"expiration": "13410651112845113", "last_modified": "13402875112845127", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:64519,*": {"expiration": "13410651843225321", "last_modified": "13402875843225334", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:64646,*": {"expiration": "13410998120125239", "last_modified": "13403222120125250", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:65053,*": {"expiration": "13410998772665188", "last_modified": "13403222772665203", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:65311,*": {"expiration": "13410652046495878", "last_modified": "13402876046495889", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"http://localhost:49177,*": {"last_modified": "13403223133418633", "setting": {"lastEngagementTime": 1.3403223133418604e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 8.999999999999998, "rawScore": 8.999999999999998}}, "http://localhost:49487,*": {"last_modified": "13403216601999976", "setting": {"lastEngagementTime": 1.3403179893619904e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.2}}, "http://localhost:50137,*": {"last_modified": "13403224039933606", "setting": {"lastEngagementTime": 1.3403224039933568e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 7.799999999999998, "rawScore": 7.799999999999998}}, "http://localhost:50204,*": {"last_modified": "13403216601999899", "setting": {"lastEngagementTime": 1.340318072150808e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 7.199999999999998}}, "http://localhost:50255,*": {"last_modified": "13403216601999823", "setting": {"lastEngagementTime": 1.340314934352274e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.8}}, "http://localhost:50681,*": {"last_modified": "13403216601999751", "setting": {"lastEngagementTime": 1.340314971974704e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.3999999999999995}}, "http://localhost:50703,*": {"last_modified": "13403216601999673", "setting": {"lastEngagementTime": 1.340318117735066e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.3999999999999995}}, "http://localhost:51176,*": {"last_modified": "13403225094814562", "setting": {"lastEngagementTime": 1.3403225094814526e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.999999999999999, "rawScore": 5.999999999999999}}, "http://localhost:51320,*": {"last_modified": "13403216601999592", "setting": {"lastEngagementTime": 1.3403182012337572e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.8}}, "http://localhost:51430,*": {"last_modified": "13403216601999516", "setting": {"lastEngagementTime": 1.3403150399434004e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.999999999999999}}, "http://localhost:52145,*": {"last_modified": "13403216601999440", "setting": {"lastEngagementTime": 1.3403151055186056e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.8}}, "http://localhost:53305,*": {"last_modified": "13403216601999362", "setting": {"lastEngagementTime": 1.340314789642302e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.3999999999999995}}, "http://localhost:53686,*": {"last_modified": "13403216601999285", "setting": {"lastEngagementTime": 1.3403148188076856e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.2}}, "http://localhost:53973,*": {"last_modified": "13403216601999207", "setting": {"lastEngagementTime": 1.3403148431432812e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.2}}, "http://localhost:54285,*": {"last_modified": "13403216601999132", "setting": {"lastEngagementTime": 1.3403185362101052e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "http://localhost:54433,*": {"last_modified": "13403216601999060", "setting": {"lastEngagementTime": 1.3403185640896424e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.999999999999999}}, "http://localhost:54834,*": {"last_modified": "13403216601998991", "setting": {"lastEngagementTime": 1.3403186002993048e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.8}}, "http://localhost:55221,*": {"last_modified": "13403216601998926", "setting": {"lastEngagementTime": 1.3403186437713224e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.8}}, "http://localhost:55864,*": {"last_modified": "13403216601998861", "setting": {"lastEngagementTime": 1.3403187123203568e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.3999999999999995}}, "http://localhost:56407,*": {"last_modified": "13403216601998799", "setting": {"lastEngagementTime": 1.34031878019978e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.999999999999999}}, "http://localhost:57964,*": {"last_modified": "13403216696954972", "setting": {"lastEngagementTime": 1.3403216696954944e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.3999999999999995, "rawScore": 5.3999999999999995}}, "http://localhost:59204,*": {"last_modified": "13403218103795423", "setting": {"lastEngagementTime": 1.3403218103795396e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.8, "rawScore": 4.8}}, "http://localhost:60335,*": {"last_modified": "13403216601998732", "setting": {"lastEngagementTime": 1.3403113898180684e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.6}}, "http://localhost:61972,*": {"last_modified": "13403219096148669", "setting": {"lastEngagementTime": 1.340321909614864e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.999999999999999, "rawScore": 5.999999999999999}}, "http://localhost:62438,*": {"last_modified": "13403216601998659", "setting": {"lastEngagementTime": 1.3403116322781932e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 11.399999999999997}}, "http://localhost:62459,*": {"last_modified": "13403219761529238", "setting": {"lastEngagementTime": 1.3403219761529204e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.3999999999999995, "rawScore": 5.3999999999999995}}, "http://localhost:63071,*": {"last_modified": "13403216601998585", "setting": {"lastEngagementTime": 1.3403084851525572e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.3999999999999995}}, "http://localhost:63241,*": {"last_modified": "13403220446095388", "setting": {"lastEngagementTime": 1.340322044609536e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 7.799999999999998, "rawScore": 7.799999999999998}}, "http://localhost:63331,*": {"last_modified": "13403216601998512", "setting": {"lastEngagementTime": 1.3403084965844032e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.2}}, "http://localhost:63665,*": {"last_modified": "13403220855239174", "setting": {"lastEngagementTime": 1.340322085523914e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.8, "rawScore": 4.8}}, "http://localhost:63691,*": {"last_modified": "13403216601998416", "setting": {"lastEngagementTime": 1.3403117404542424e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.2}}, "http://localhost:63899,*": {"last_modified": "13403221242854091", "setting": {"lastEngagementTime": 1.3403221242854054e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.999999999999999, "rawScore": 5.999999999999999}}, "http://localhost:63960,*": {"last_modified": "13403216601998357", "setting": {"lastEngagementTime": 1.340311763163528e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "http://localhost:64238,*": {"last_modified": "13403216601998293", "setting": {"lastEngagementTime": 1.3403117996615972e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.8}}, "http://localhost:64519,*": {"last_modified": "13403216601998226", "setting": {"lastEngagementTime": 1.3403118309681224e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.8}}, "http://localhost:64646,*": {"last_modified": "13403222029203077", "setting": {"lastEngagementTime": 1.3403222029203044e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.8, "rawScore": 4.8}}, "http://localhost:65053,*": {"last_modified": "13403222259115976", "setting": {"lastEngagementTime": 1.3403222259115946e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.3999999999999995, "rawScore": 5.3999999999999995}}, "http://localhost:65311,*": {"last_modified": "13403216601998064", "setting": {"lastEngagementTime": 1.3403119021295828e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.8}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "139.0.7258.139", "creation_time": "13402703958266995", "exit_type": "Normal", "family_member_role": "not_in_family", "isolated_web_app": {"install": {"pending_initialization_count": 0}}, "last_engagement_time": "13403225094814526", "last_time_obsolete_http_credentials_removed": 1758230418.342136, "last_time_password_store_metrics_reported": 1758743031.779355, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Votre Chrome", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13403475866083671", "hash_real_time_ohttp_key": "3gAgIdAPvswXJvG0X2iJDRTbvAipcg7VHYjbBSEc6BQPGRUABAABAAI=", "metrics_last_log_time": "13403216601", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQjOLj5fjE5xcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEOzi4+X4xOcXCuUCChFjcm9zc19kZXZpY2VfdXNlchLPAgrDAg0AAIA/EOO64cLwuucXGrACCqcCGqQCChkNAACAPxISTm9Dcm9zc0RldmljZVVzYWdlChgNAAAAQBIRQ3Jvc3NEZXZpY2VNb2JpbGUKGQ0AAEBAEhJDcm9zc0RldmljZURlc2t0b3AKGA0AAIBAEhFDcm9zc0RldmljZVRhYmxldAoiDQAAoEASG0Nyb3NzRGV2aWNlTW9iaWxlQW5kRGVza3RvcAohDQAAwEASGkNyb3NzRGV2aWNlTW9iaWxlQW5kVGFibGV0CiINAADgQBIbQ3Jvc3NEZXZpY2VEZXNrdG9wQW5kVGFibGV0CiANAAAAQRIZQ3Jvc3NEZXZpY2VBbGxEZXZpY2VUeXBlcwoXDQAAEEESEENyb3NzRGV2aWNlT3RoZXISEk5vQ3Jvc3NEZXZpY2VVc2FnZRIEEAcYBCACEN674cLwuucX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13403059199000000", "uma_in_sql_start_time": "13402703958341513"}, "selectfile": {"last_directory": "C:\\Users\\<USER>\\Pictures"}, "sessions": {"event_log": [{"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13403219420666056", "type": 2, "window_count": 1}, {"crashed": false, "time": "13403219487969161", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13403220260601507", "type": 2, "window_count": 1}, {"crashed": false, "time": "13403220329448181", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13403220701730042", "type": 2, "window_count": 1}, {"crashed": false, "time": "13403220780161977", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13403220884681923", "type": 2, "window_count": 1}, {"crashed": false, "time": "13403220959258846", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13403221405356845", "type": 2, "window_count": 1}, {"crashed": false, "time": "13403221750689692", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13403222120114908", "type": 2, "window_count": 1}, {"crashed": false, "time": "13403222194450478", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13403222772653491", "type": 2, "window_count": 1}, {"crashed": false, "time": "13403222900169276", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13403223609595153", "type": 2, "window_count": 1}, {"crashed": false, "time": "13403223750719869", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13403224896373531", "type": 2, "window_count": 1}, {"crashed": false, "time": "13403225022727282", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13403225547035008", "type": 2, "window_count": 1}], "session_data_status": 5}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "site_search_settings": {"overridden_keywords": []}, "spellcheck": {"dictionaries": ["fr-FR", "fr"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "139"}}