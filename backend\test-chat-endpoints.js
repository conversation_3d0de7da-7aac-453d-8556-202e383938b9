const axios = require('axios');
require('dotenv').config();

// Configuration
const BASE_URL = process.env.BASE_URL || 'https://abe4bbb5eca9.ngrok-free.app';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2OGE0NzQ4ZjdjYjRjOWYxMzQyOGJlOGYiLCJyb2xlIjoiY29uZHVjdGV1ciIsImlhdCI6MTc1ODc0NjExMCwiZXhwIjoxNzU4NzQ5NzEwfQ.GD3FAW8oW0-EApH-rYGmaEtbZmxlMxNkm_yMYsCNsSo';

// Headers communs
const headers = {
  'Content-Type': 'application/json',
  'ngrok-skip-browser-warning': 'true',
  'User-Agent': 'Test Script',
  'Accept': 'application/json',
  'Authorization': `Bearer ${TEST_TOKEN}`
};

console.log('🧪 Test des endpoints de chat');
console.log(`🌐 URL de base: ${BASE_URL}`);
console.log(`🔑 Token utilisé: ${TEST_TOKEN.substring(0, 50)}...`);

async function testEndpoint(endpoint, method = 'GET', data = null) {
  try {
    console.log(`\n📋 Test ${method} ${endpoint}`);
    
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers,
      timeout: 30000
    };
    
    if (data && (method === 'POST' || method === 'PUT')) {
      config.data = data;
    }
    
    const response = await axios(config);
    console.log(`✅ Succès - Status: ${response.status}`);
    console.log(`📄 Réponse: ${JSON.stringify(response.data, null, 2)}`);
    return { success: true, status: response.status, data: response.data };
  } catch (error) {
    console.log(`❌ Erreur - Status: ${error.response?.status || 'N/A'}`);
    console.log(`📄 Message: ${error.response?.data?.message || error.message}`);
    console.log(`📄 Réponse complète: ${JSON.stringify(error.response?.data, null, 2)}`);
    return { success: false, status: error.response?.status, error: error.response?.data || error.message };
  }
}

async function runTests() {
  console.log('\n🚀 Démarrage des tests...\n');
  
  // Test 1: Health check
  await testEndpoint('/api/health');
  
  // Test 2: Conversations
  await testEndpoint('/api/chat/conversations');
  
  // Test 3: Recherche d'utilisateurs
  await testEndpoint('/api/chat/users/search?query=test');
  
  // Test 4: Recherche d'utilisateurs avec rôle
  await testEndpoint('/api/chat/users/search?query=ghada&role=conducteur');
  
  // Test 5: Compteur de messages non lus
  await testEndpoint('/api/chat/messages/unread-count');
  
  console.log('\n✅ Tests terminés');
}

// Exécuter les tests
runTests().catch(console.error);
